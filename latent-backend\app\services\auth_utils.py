from fastapi import Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict,Text, Any
import jwt
import httpx
import os
from datetime import datetime, timezone
from dotenv import load_dotenv


load_dotenv()

# Get Supabase URL and ensure it has the proper format
SUPABASE_URL = os.getenv("SUPABASE_URL")
if not SUPABASE_URL:
    raise ValueError("SUPABASE_URL environment variable is not set")

SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY")
if not SUPABASE_PUBLIC_KEY:
    raise ValueError("SUPABASE_PUBLIC_KEY environment variable is not set")

SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")
if not SUPABASE_SERVICE_KEY:
    raise ValueError("SUPABASE_SERVICE_KEY environment variable is not set")

def getHeaders(payload: Any):
    return {
        "apikey": SUPABASE_PUBLIC_KEY,
        "Authorization": f"Bearer {payload.get('access_token', '')}",
        "Content-Type": "application/json"
    }

# Get security scheme that was defined in user_management.py
security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[Any, Any]:
    token = credentials.credentials
    try:
        print(f"Verifying token:\n{token}")  # Print first part of token for debugging
        
        # Decode token without verification for development
        # We still want to check the token format but don't need to verify the signature
        payload = jwt.decode(
            token, 
            options={"verify_signature": False}
        )
        
        # if the payload is expired, raise 401 and ask frontend to refresh the token
        print(f"Decoded token claims: {payload}")
        
          # Check if token has expired
        exp = payload.get('exp')
        if exp is not None:
            current_time = datetime.now(timezone.utc).timestamp()
            print(current_time, exp, 'current_time')
            if current_time > exp:
                raise HTTPException(
                    status_code=401,
                    detail="Token has expired"
                )
        # Check if the token has basic required claims
        email = payload.get('email')
        if not email:
            raise HTTPException(status_code=401, detail="Invalid token: missing email claim")

        try:
            # Check if user is still active and exists
            user_status = await check_email_and_user_status(email)
            print('user_status',user_status.text)
        except HTTPException as e:
            # Propagate all 401 errors from check_email_and_user_status
            if e.status_code == 401:
                raise HTTPException(status_code=401, detail=e.detail)
            # Re-raise other errors
            raise

        # Store the token itself in the payload so we can use it for Supabase API calls
        payload['access_token'] = token
            
        return payload
    except Exception as e:
        print(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Authentication error: {str(e)}")


async def check_email_and_user_status(email: str) -> Dict[str, Any]:
    print('check_email_and_user_status', email)

    try:
        async with httpx.AsyncClient() as client:
            # Step 1: Check allowed_emails table
            allowed_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/allowed_emails",
                headers={
                    "apikey": SUPABASE_SERVICE_KEY,
                    "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                    "Content-Type": "application/json"
                },
                params={
                    "email": f"eq.{email}",
                    "select": "id,organization_id,is_active"
                }   
            )
            print(allowed_response.text, 'allowed_response')
            if allowed_response.status_code != 200:
                raise HTTPException(status_code=500, detail="Error checking allowed emails")

            allowed_data = allowed_response.json()
            if not allowed_data or len(allowed_data) == 0:
                raise HTTPException(status_code=401, detail="Email not authorized to access the system")

            # Check if allowed email is active
            if not allowed_data[0].get("is_active", False):
                raise HTTPException(status_code=401, detail="this email is marked as Inactive")


            # Step 2: Check if user exists in users table
            user_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers={
                    "apikey": SUPABASE_SERVICE_KEY,
                    "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                    "Content-Type": "application/json"
                },
                params={
                    "email": f"eq.{email}",
                    "select": "id"
                }
            )
            print('user_response', user_response.text)
            if user_response.status_code != 200:
                raise HTTPException(status_code=500, detail="Error checking user status")

            user_data = user_response.json()
            if not user_data:
                raise HTTPException(status_code=401, detail="User is allowed but hasn't signed up yet")

            return {
                "email": email,
                "is_allowed": True,
                "user_exists": True,
                "is_active": True,
                "organization_id": allowed_data[0].get("organization_id"),
                "message": "Success"
            }

    except HTTPException:
        raise
    except Exception as e:
        print("Unhandled exception:", str(e))
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")