from fastapi import Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict,Text, Any
import jwt
import httpx
import os
from datetime import datetime, timezone
from dotenv import load_dotenv


load_dotenv()

# Get Supabase URL and ensure it has the proper format
SUPABASE_URL = os.getenv("SUPABASE_URL")
if not SUPABASE_URL:
    raise ValueError("SUPABASE_URL environment variable is not set")

SUPABASE_PUBLIC_KEY = os.getenv("SUPABASE_PUBLIC_KEY")
if not SUPABASE_PUBLIC_KEY:
    raise ValueError("SUPABASE_PUBLIC_KEY environment variable is not set")

SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")
if not SUPABASE_SERVICE_KEY:
    raise ValueError("SUPABASE_SERVICE_KEY environment variable is not set")

def getHeaders(payload: Any):
    return {
        "apikey": SUPABASE_PUBLIC_KEY,
        "Authorization": f"Bearer {payload.get('access_token', '')}",
        "Content-Type": "application/json"
    }

# Get security scheme that was defined in user_management.py
security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[Any, Any]:
    token = credentials.credentials
    try:
        print(f"Verifying token:\n{token}")  # Print first part of token for debugging
        
        # Decode token without verification for development
        # We still want to check the token format but don't need to verify the signature
        payload = jwt.decode(
            token, 
            options={"verify_signature": False}
        )
        
        # if the payload is expired, raise 401 and ask frontend to refresh the token
        print(f"Decoded token claims: {payload}")
        
          # Check if token has expired
        exp = payload.get('exp')
        if exp is not None:
            current_time = datetime.now(timezone.utc).timestamp()
            print(current_time, exp, 'current_time')
            if current_time > exp:
                raise HTTPException(
                    status_code=401,
                    detail="Token has expired"
                )
        # Check if the token has basic required claims
        email = payload.get('email')
        if not email:
            raise HTTPException(status_code=401, detail="Invalid token: missing email claim")

        try:
            # Check if user is still active and exists
            user_status = await check_email_and_user_status(email)
            print('user_status',user_status.text)
        except HTTPException as e:
            # Propagate all 401 errors from check_email_and_user_status
            if e.status_code == 401:
                raise HTTPException(status_code=401, detail=e.detail)
            # Re-raise other errors
            raise

        # Store the token itself in the payload so we can use it for Supabase API calls
        payload['access_token'] = token
            
        return payload
    except Exception as e:
        print(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Authentication error: {str(e)}")


async def check_email_and_user_status(email: str) -> Dict[str, Any]:
    print('check_email_and_user_status', email)

    try:
        async with httpx.AsyncClient() as client:
            # Step 1: Check allowed_emails table using service role with RLS bypass
            allowed_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/allowed_emails",
                headers={
                    "apikey": SUPABASE_SERVICE_KEY,
                    "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                    "Content-Type": "application/json",
                    "X-Client-Info": "supabase-py/2.0.0"
                },
                params={
                    "email": f"eq.{email}",
                    "select": "id,organization_id,is_active,expiry_date"
                }
            )
            print(f"Allowed response status: {allowed_response.status_code}")
            print(f"Allowed response text: {allowed_response.text}")

            # If we get a 401 or 403, it might be RLS blocking us
            # Try a different approach - query all and filter in Python
            if allowed_response.status_code in [401, 403]:
                print("RLS might be blocking, trying alternative approach...")
                # Try to get all allowed emails and filter in Python
                all_allowed_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/allowed_emails",
                    headers={
                        "apikey": SUPABASE_SERVICE_KEY,
                        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                        "Content-Type": "application/json"
                    },
                    params={
                        "select": "id,email,organization_id,is_active,expiry_date"
                    }
                )
                print(f"All allowed response status: {all_allowed_response.status_code}")
                print(f"All allowed response text: {all_allowed_response.text}")

                if all_allowed_response.status_code == 200:
                    all_allowed_data = all_allowed_response.json()
                    # Filter for our specific email
                    allowed_data = [item for item in all_allowed_data if item.get('email') == email]
                    if not allowed_data:
                        raise HTTPException(status_code=401, detail="Email not authorized to access the system")
                else:
                    raise HTTPException(status_code=500, detail=f"Error checking allowed emails: {all_allowed_response.text}")
            elif allowed_response.status_code != 200:
                print(f"Error response from allowed_emails: {allowed_response.status_code} - {allowed_response.text}")
                raise HTTPException(status_code=500, detail=f"Error checking allowed emails: {allowed_response.text}")
            else:
                allowed_data = allowed_response.json()
                if not allowed_data or len(allowed_data) == 0:
                    raise HTTPException(status_code=401, detail="Email not authorized to access the system")

            # Check if allowed email is active
            if not allowed_data[0].get("is_active", False):
                raise HTTPException(status_code=401, detail="this email is marked as Inactive")

            # Check if email has expired
            expiry_date = allowed_data[0].get("expiry_date")
            if expiry_date:
                from datetime import datetime
                try:
                    expiry_dt = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))
                    if datetime.now(timezone.utc) > expiry_dt:
                        raise HTTPException(status_code=401, detail="Email access has expired")
                except Exception as e:
                    print(f"Error parsing expiry date: {e}")

            # Step 2: Check if user exists in users table using service role
            user_response = await client.get(
                f"{SUPABASE_URL}/rest/v1/users",
                headers={
                    "apikey": SUPABASE_SERVICE_KEY,
                    "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                    "Content-Type": "application/json",
                    "X-Client-Info": "supabase-py/2.0.0"
                },
                params={
                    "email": f"eq.{email}",
                    "select": "id,organization_id"
                }
            )
            print(f"User response status: {user_response.status_code}")
            print(f"User response text: {user_response.text}")

            # Similar fallback for users table
            if user_response.status_code in [401, 403]:
                print("RLS might be blocking users query, trying alternative approach...")
                all_users_response = await client.get(
                    f"{SUPABASE_URL}/rest/v1/users",
                    headers={
                        "apikey": SUPABASE_SERVICE_KEY,
                        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                        "Content-Type": "application/json"
                    },
                    params={
                        "select": "id,email,organization_id"
                    }
                )
                print(f"All users response status: {all_users_response.status_code}")
                print(f"All users response text: {all_users_response.text}")

                if all_users_response.status_code == 200:
                    all_users_data = all_users_response.json()
                    # Filter for our specific email
                    user_data = [item for item in all_users_data if item.get('email') == email]
                    if not user_data:
                        raise HTTPException(status_code=401, detail="User is allowed but hasn't signed up yet")
                else:
                    raise HTTPException(status_code=500, detail=f"Error checking user status: {all_users_response.text}")
            elif user_response.status_code != 200:
                print(f"Error response from users: {user_response.status_code} - {user_response.text}")
                raise HTTPException(status_code=500, detail=f"Error checking user status: {user_response.text}")
            else:
                user_data = user_response.json()
                if not user_data or len(user_data) == 0:
                    raise HTTPException(status_code=401, detail="User is allowed but hasn't signed up yet")

            return {
                "email": email,
                "is_allowed": True,
                "user_exists": True,
                "is_active": True,
                "organization_id": allowed_data[0].get("organization_id"),
                "message": "Success"
            }

    except HTTPException:
        raise
    except Exception as e:
        print("Unhandled exception:", str(e))
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")