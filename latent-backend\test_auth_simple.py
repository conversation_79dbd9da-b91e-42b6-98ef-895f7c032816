#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.auth_utils import check_email_and_user_status

async def test_auth():
    """Simple test of the authentication function"""
    
    # Test with the email from the signup example
    test_email = "<EMAIL>"
    
    print(f"Testing authentication for: {test_email}")
    
    try:
        result = await check_email_and_user_status(test_email)
        print(f"✅ Success: {result}")
        return True
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_auth())
    if success:
        print("\n🎉 Authentication test passed!")
    else:
        print("\n💥 Authentication test failed!")
        sys.exit(1)
