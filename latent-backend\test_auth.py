#!/usr/bin/env python3

import asyncio
import httpx
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")

async def test_auth_queries():
    """Test the authentication queries directly"""
    
    # Test email - using the email from signup example
    test_email = "<EMAIL>"  # This should be in allowed_emails table
    
    print(f"Testing authentication for email: {test_email}")
    print(f"Supabase URL: {SUPABASE_URL}")
    print(f"Service key (first 20 chars): {SUPABASE_SERVICE_KEY[:20]}...")
    
    async with httpx.AsyncClient() as client:
        # Test 1: Check allowed_emails table
        print("\n=== Testing allowed_emails query ===")
        allowed_response = await client.get(
            f"{SUPABASE_URL}/rest/v1/allowed_emails",
            headers={
                "apikey": SUPABASE_SERVICE_KEY,
                "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                "Content-Type": "application/json"
            },
            params={
                "email": f"eq.{test_email}",
                "select": "id,organization_id,is_active,expiry_date"
            }   
        )
        
        print(f"Status Code: {allowed_response.status_code}")
        print(f"Response Headers: {dict(allowed_response.headers)}")
        print(f"Response Text: {allowed_response.text}")
        
        # Test 2: Check users table
        print("\n=== Testing users query ===")
        user_response = await client.get(
            f"{SUPABASE_URL}/rest/v1/users",
            headers={
                "apikey": SUPABASE_SERVICE_KEY,
                "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                "Content-Type": "application/json"
            },
            params={
                "email": f"eq.{test_email}",
                "select": "id,organization_id"
            }
        )
        
        print(f"Status Code: {user_response.status_code}")
        print(f"Response Headers: {dict(user_response.headers)}")
        print(f"Response Text: {user_response.text}")
        
        # Test 3: Try to get all allowed emails (to see if service key works at all)
        print("\n=== Testing service key access (get all allowed_emails) ===")
        all_allowed_response = await client.get(
            f"{SUPABASE_URL}/rest/v1/allowed_emails",
            headers={
                "apikey": SUPABASE_SERVICE_KEY,
                "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
                "Content-Type": "application/json"
            },
            params={
                "select": "email,is_active,organization_id"
            }   
        )
        
        print(f"Status Code: {all_allowed_response.status_code}")
        print(f"Response Text: {all_allowed_response.text}")

if __name__ == "__main__":
    asyncio.run(test_auth_queries())
