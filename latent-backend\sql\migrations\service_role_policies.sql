-- ===========================================
-- SERVICE ROLE POLICIES
-- ===========================================
-- Add policies to allow service role to bypass RLS for authentication checks

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Service role can read allowed emails" ON allowed_emails;
DROP POLICY IF EXISTS "Service role can read users" ON users;

-- Allow service role to read allowed_emails for authentication purposes
CREATE POLICY "Service role can read allowed emails"
ON allowed_emails
FOR SELECT
TO service_role
USING (true);

-- Allow service role to read users for authentication purposes
CREATE POLICY "Service role can read users"
ON users
FOR SELECT
TO service_role
USING (true);

-- Also modify existing policies to allow service role access
-- Update the anonymous policy to also work for service role
DROP POLICY IF EXISTS "Anonymous users can check if an email is allowed" ON allowed_emails;
CREATE POLICY "Anonymous users can check if an email is allowed"
ON allowed_emails
FOR SELECT
USING (
  email = current_setting('request.jwt.claims', true)::json->>'email'
  OR current_setting('role', true) = 'service_role'
);
